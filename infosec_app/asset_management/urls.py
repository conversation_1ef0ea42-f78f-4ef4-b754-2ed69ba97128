from django.urls import path
from . import views

app_name = 'asset_management'

urlpatterns = [
    # Dashboard
    path('', views.dashboard_view, name='dashboard'),

    # Asset CRUD operations
    path('assets/', views.AssetListView.as_view(), name='asset_list'),
    path('assets/add/', views.AssetCreateView.as_view(), name='asset_create'),
    path('assets/<int:pk>/', views.AssetDetailView.as_view(), name='asset_detail'),
    path('assets/<int:pk>/edit/', views.AssetUpdateView.as_view(), name='asset_update'),
    path('assets/<int:pk>/delete/', views.AssetDeleteView.as_view(), name='asset_delete'),

    # Location management
    path('locations/add/', views.LocationCreateView.as_view(), name='location_create'),
    path('api/locations/', views.get_locations_json, name='locations_json'),
]
