from django.db import models
from django.urls import reverse
from django.utils import timezone


class Asset(models.Model):
    """
    Model representing an information security asset
    """
    ASSET_TYPES = [
        ('hardware', 'Hardware'),
        ('software', 'Software'),
        ('data', 'Data'),
        ('network', 'Network'),
        ('facility', 'Facility'),
        ('personnel', 'Personnel'),
    ]

    CRITICALITY_LEVELS = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ]

    STATUS_CHOICES = [
        ('active', 'Active'),
        ('inactive', 'Inactive'),
        ('maintenance', 'Under Maintenance'),
        ('decommissioned', 'Decommissioned'),
    ]

    # Basic Information
    name = models.CharField(max_length=200, help_text="Asset name or identifier")
    description = models.TextField(blank=True, help_text="Detailed description of the asset")
    asset_type = models.CharField(max_length=20, choices=ASSET_TYPES, help_text="Type of asset")

    # Asset Details
    serial_number = models.CharField(max_length=100, blank=True, help_text="Serial number or unique identifier")
    location = models.CharField(max_length=200, blank=True, help_text="Physical or logical location")
    owner = models.CharField(max_length=100, blank=True, help_text="Asset owner or responsible person")

    # Security Information
    criticality = models.CharField(max_length=20, choices=CRITICALITY_LEVELS, default='medium',
                                 help_text="Business criticality level")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active',
                            help_text="Current operational status")

    # Dates
    acquisition_date = models.DateField(null=True, blank=True, help_text="Date when asset was acquired")
    last_updated = models.DateTimeField(auto_now=True)
    created_date = models.DateTimeField(auto_now_add=True)

    # Additional Information
    notes = models.TextField(blank=True, help_text="Additional notes or comments")

    class Meta:
        ordering = ['name']
        verbose_name = 'Asset'
        verbose_name_plural = 'Assets'

    def __str__(self):
        return f"{self.name} ({self.get_asset_type_display()})"

    def get_absolute_url(self):
        return reverse('asset_management:asset_detail', kwargs={'pk': self.pk})
