from django.db import models
from django.urls import reverse
from django.utils import timezone


class AssetType(models.Model):
    """
    Model representing different types of assets that can be managed
    """
    name = models.CharField(max_length=100, unique=True, help_text="Asset type name (e.g., Hardware, Software)")
    description = models.TextField(blank=True, help_text="Description of this asset type")
    is_active = models.BooleanField(default=True, help_text="Whether this asset type is currently in use")
    created_date = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['name']
        verbose_name = 'Asset Type'
        verbose_name_plural = 'Asset Types'

    def __str__(self):
        return self.name


class Location(models.Model):
    """
    Model representing different locations where assets can be placed
    """
    name = models.CharField(max_length=200, unique=True, help_text="Location name (e.g., Data Center A, Office Floor 2)")
    address = models.TextField(blank=True, help_text="Physical address or detailed location description")
    is_active = models.<PERSON><PERSON>anField(default=True, help_text="Whether this location is currently in use")
    created_date = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['name']
        verbose_name = 'Location'
        verbose_name_plural = 'Locations'

    def __str__(self):
        return self.name


class Asset(models.Model):
    """
    Model representing an information security asset
    """
    ASSET_TYPES = [
        ('hardware', 'Hardware'),
        ('software', 'Software'),
        ('data', 'Data'),
        ('network', 'Network'),
        ('facility', 'Facility'),
        ('personnel', 'Personnel'),
    ]

    CRITICALITY_LEVELS = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ]

    STATUS_CHOICES = [
        ('active', 'Active'),
        ('inactive', 'Inactive'),
        ('maintenance', 'Under Maintenance'),
        ('decommissioned', 'Decommissioned'),
    ]

    # Basic Information
    name = models.CharField(max_length=200, help_text="Asset name or identifier")
    description = models.TextField(blank=True, help_text="Detailed description of the asset")
    asset_type = models.CharField(max_length=20, choices=ASSET_TYPES, help_text="Type of asset")

    # New ForeignKey fields (will be added in migration)
    asset_type_fk = models.ForeignKey(AssetType, on_delete=models.PROTECT, null=True, blank=True, help_text="Type of asset (new)")
    location_fk = models.ForeignKey(Location, on_delete=models.SET_NULL, null=True, blank=True, help_text="Physical or logical location (new)")

    # Asset Details
    serial_number = models.CharField(max_length=100, blank=True, help_text="Serial number or unique identifier")
    location = models.CharField(max_length=200, blank=True, help_text="Physical or logical location")
    owner = models.CharField(max_length=100, blank=True, help_text="Asset owner or responsible person")

    # Security Information
    criticality = models.CharField(max_length=20, choices=CRITICALITY_LEVELS, default='medium',
                                 help_text="Business criticality level")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active',
                            help_text="Current operational status")

    # Dates
    acquisition_date = models.DateField(null=True, blank=True, help_text="Date when asset was acquired")
    last_updated = models.DateTimeField(auto_now=True)
    created_date = models.DateTimeField(auto_now_add=True)

    # Additional Information
    notes = models.TextField(blank=True, help_text="Additional notes or comments")

    class Meta:
        ordering = ['name']
        verbose_name = 'Asset'
        verbose_name_plural = 'Assets'

    def __str__(self):
        return f"{self.name} ({self.asset_type.name if self.asset_type else 'No Type'})"

    def get_absolute_url(self):
        return reverse('asset_management:asset_detail', kwargs={'pk': self.pk})
