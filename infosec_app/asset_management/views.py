from django.shortcuts import render, get_object_or_404, redirect
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.db.models import Q
from .models import Asset


class AssetListView(ListView):
    """
    View to display list of all assets with search functionality
    """
    model = Asset
    template_name = 'asset_management/asset_list.html'
    context_object_name = 'assets'
    paginate_by = 20

    def get_queryset(self):
        queryset = Asset.objects.all()
        search_query = self.request.GET.get('search')

        if search_query:
            queryset = queryset.filter(
                Q(name__icontains=search_query) |
                Q(description__icontains=search_query) |
                Q(asset_type__icontains=search_query) |
                Q(owner__icontains=search_query) |
                Q(location__icontains=search_query)
            )

        return queryset.order_by('name')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_query'] = self.request.GET.get('search', '')
        return context


class AssetDetailView(DetailView):
    """
    View to display detailed information about a specific asset
    """
    model = Asset
    template_name = 'asset_management/asset_detail.html'
    context_object_name = 'asset'


class AssetCreateView(CreateView):
    """
    View to create a new asset
    """
    model = Asset
    template_name = 'asset_management/asset_form.html'
    fields = [
        'name', 'description', 'asset_type', 'serial_number',
        'location', 'owner', 'criticality', 'status', 'acquisition_date', 'notes'
    ]
    success_url = reverse_lazy('asset_management:asset_list')

    def form_valid(self, form):
        messages.success(self.request, f'Asset "{form.instance.name}" has been created successfully.')
        return super().form_valid(form)


class AssetUpdateView(UpdateView):
    """
    View to update an existing asset
    """
    model = Asset
    template_name = 'asset_management/asset_form.html'
    fields = [
        'name', 'description', 'asset_type', 'serial_number',
        'location', 'owner', 'criticality', 'status', 'acquisition_date', 'notes'
    ]
    success_url = reverse_lazy('asset_management:asset_list')

    def form_valid(self, form):
        messages.success(self.request, f'Asset "{form.instance.name}" has been updated successfully.')
        return super().form_valid(form)


class AssetDeleteView(DeleteView):
    """
    View to delete an asset
    """
    model = Asset
    template_name = 'asset_management/asset_confirm_delete.html'
    success_url = reverse_lazy('asset_management:asset_list')

    def delete(self, request, *args, **kwargs):
        asset = self.get_object()
        messages.success(request, f'Asset "{asset.name}" has been deleted successfully.')
        return super().delete(request, *args, **kwargs)


# Function-based view for dashboard (optional)
def dashboard_view(request):
    """
    Dashboard view showing asset statistics
    """
    total_assets = Asset.objects.count()
    active_assets = Asset.objects.filter(status='active').count()
    critical_assets = Asset.objects.filter(criticality='critical').count()

    # Asset type distribution
    asset_types = Asset.objects.values('asset_type').distinct()
    type_counts = {}
    for asset_type in asset_types:
        type_name = asset_type['asset_type']
        count = Asset.objects.filter(asset_type=type_name).count()
        type_counts[type_name] = count

    context = {
        'total_assets': total_assets,
        'active_assets': active_assets,
        'critical_assets': critical_assets,
        'type_counts': type_counts,
    }

    return render(request, 'asset_management/dashboard.html', context)
