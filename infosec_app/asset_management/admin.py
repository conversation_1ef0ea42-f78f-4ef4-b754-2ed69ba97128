from django.contrib import admin
from .models import Asset


@admin.register(Asset)
class AssetAdmin(admin.ModelAdmin):
    list_display = ['name', 'asset_type', 'owner', 'location', 'criticality', 'status', 'created_date']
    list_filter = ['asset_type', 'criticality', 'status', 'created_date']
    search_fields = ['name', 'description', 'owner', 'location', 'serial_number']
    list_editable = ['status', 'criticality']
    date_hierarchy = 'created_date'
    ordering = ['name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'description', 'asset_type')
        }),
        ('Asset Details', {
            'fields': ('serial_number', 'location', 'owner')
        }),
        ('Security & Status', {
            'fields': ('criticality', 'status', 'acquisition_date')
        }),
        ('Additional Information', {
            'fields': ('notes',),
            'classes': ('collapse',)
        }),
    )
