from django.contrib import admin
from .models import Asset, AssetType, Location


@admin.register(AssetType)
class AssetTypeAdmin(admin.ModelAdmin):
    list_display = ['name', 'description', 'is_active', 'created_date']
    list_filter = ['is_active', 'created_date']
    search_fields = ['name', 'description']
    list_editable = ['is_active']
    ordering = ['name']

    fieldsets = (
        ('Asset Type Information', {
            'fields': ('name', 'description', 'is_active')
        }),
    )


@admin.register(Location)
class LocationAdmin(admin.ModelAdmin):
    list_display = ['name', 'address', 'is_active', 'created_date']
    list_filter = ['is_active', 'created_date']
    search_fields = ['name', 'address']
    list_editable = ['is_active']
    ordering = ['name']

    fieldsets = (
        ('Location Information', {
            'fields': ('name', 'address', 'is_active')
        }),
    )


@admin.register(Asset)
class AssetAdmin(admin.ModelAdmin):
    list_display = ['name', 'asset_type', 'owner', 'location', 'criticality', 'status', 'created_date']
    list_filter = ['asset_type', 'location', 'criticality', 'status', 'created_date']
    search_fields = ['name', 'description', 'owner', 'serial_number', 'asset_type__name', 'location__name']
    list_editable = ['status', 'criticality']
    date_hierarchy = 'created_date'
    ordering = ['name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'description', 'asset_type')
        }),
        ('Asset Details', {
            'fields': ('serial_number', 'location', 'owner')
        }),
        ('Security & Status', {
            'fields': ('criticality', 'status', 'acquisition_date')
        }),
        ('Additional Information', {
            'fields': ('notes',),
            'classes': ('collapse',)
        }),
    )
