<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}InfoSec Management System{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: 250px;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            transition: all 0.3s;
            z-index: 1000;
            overflow-y: auto;
        }

        .sidebar.collapsed {
            width: 70px;
        }

        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #34495e;
        }

        .sidebar-header h4 {
            margin: 0;
            font-size: 1.2rem;
            font-weight: bold;
        }

        .sidebar.collapsed .sidebar-header h4 {
            display: none;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sidebar-menu li {
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .sidebar-menu a {
            display: block;
            padding: 15px 20px;
            color: #ecf0f1;
            text-decoration: none;
            transition: all 0.3s;
        }

        .sidebar-menu a:hover {
            background-color: rgba(255,255,255,0.1);
            color: white;
            text-decoration: none;
        }

        .sidebar-menu a.active {
            background-color: #3498db;
            color: white;
        }

        .sidebar-menu i {
            width: 20px;
            margin-right: 10px;
        }

        .sidebar.collapsed .sidebar-menu a {
            text-align: center;
            padding: 15px 10px;
        }

        .sidebar.collapsed .sidebar-menu span {
            display: none;
        }

        .main-content {
            margin-left: 250px;
            transition: all 0.3s;
            min-height: 100vh;
            background-color: #f8f9fa;
        }

        .main-content.expanded {
            margin-left: 70px;
        }

        .top-navbar {
            background: white;
            padding: 15px 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .content-area {
            padding: 30px;
        }

        .toggle-btn {
            background: none;
            border: none;
            color: #2c3e50;
            font-size: 1.2rem;
            cursor: pointer;
        }

        .asset-card {
            transition: transform 0.2s;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .asset-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }

        .criticality-badge, .status-badge {
            font-size: 0.8em;
        }

        .page-title {
            color: #2c3e50;
            margin-bottom: 30px;
            font-weight: 600;
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 70px;
            }
            .main-content {
                margin-left: 70px;
            }
            .sidebar-header h4 {
                display: none;
            }
            .sidebar-menu span {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h4><i class="fas fa-shield-alt"></i> InfoSec System</h4>
        </div>
        <ul class="sidebar-menu">
            <li>
                <a href="{% url 'asset_management:dashboard' %}" class="{% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}">
                    <i class="fas fa-chart-bar"></i>
                    <span>Dashboard</span>
                </a>
            </li>
            <li>
                <a href="{% url 'asset_management:asset_list' %}" class="{% if 'asset' in request.resolver_match.url_name %}active{% endif %}">
                    <i class="fas fa-boxes"></i>
                    <span>Assets</span>
                </a>
            </li>
            <li>
                <a href="{% url 'asset_management:asset_create' %}">
                    <i class="fas fa-plus"></i>
                    <span>Add Asset</span>
                </a>
            </li>
            <li>
                <a href="{% url 'risk_management:dashboard' %}" class="{% if 'risk' in request.resolver_match.url_name and 'dashboard' in request.resolver_match.url_name %}active{% endif %}">
                    <i class="fas fa-chart-line"></i>
                    <span>Risk Dashboard</span>
                </a>
            </li>
            <li>
                <a href="{% url 'risk_management:risk_list' %}" class="{% if 'risk' in request.resolver_match.url_name and 'list' in request.resolver_match.url_name %}active{% endif %}">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>Risk Management</span>
                </a>
            </li>
            <li>
                <a href="{% url 'risk_management:risk_create' %}">
                    <i class="fas fa-plus-circle"></i>
                    <span>Add Risk</span>
                </a>
            </li>
            <li>
                <a href="/admin/" target="_blank">
                    <i class="fas fa-cog"></i>
                    <span>Admin Panel</span>
                </a>
            </li>
        </ul>
    </div>

    <!-- Main Content -->
    <div class="main-content" id="mainContent">
        <!-- Top Navigation -->
        <div class="top-navbar">
            <div>
                <button class="toggle-btn" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <span class="ms-3 fw-bold text-muted">{% block page_title %}InfoSec Management System{% endblock %}</span>
            </div>
            <div>
                <span class="text-muted">Welcome, Admin</span>
            </div>
        </div>

        <!-- Content Area -->
        <div class="content-area">
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}

            {% block content %}
            {% endblock %}
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');

            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');
        }
    </script>
    {% block extra_js %}{% endblock %}
</body>
</html>
