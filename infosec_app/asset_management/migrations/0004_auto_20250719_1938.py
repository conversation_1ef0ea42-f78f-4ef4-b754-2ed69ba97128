# Generated by Django 4.2.7 on 2025-07-19 19:38

from django.db import migrations


def populate_asset_types_and_locations(apps, schema_editor):
    AssetType = apps.get_model('asset_management', 'AssetType')
    Location = apps.get_model('asset_management', 'Location')
    Asset = apps.get_model('asset_management', 'Asset')

    # Create default asset types
    asset_types = [
        ('Hardware', 'Physical computing equipment and devices'),
        ('Software', 'Applications, operating systems, and digital tools'),
        ('Data', 'Information assets and databases'),
        ('Network', 'Network infrastructure and connectivity'),
        ('Facility', 'Physical locations and infrastructure'),
        ('Personnel', 'Human resources and staff'),
    ]

    asset_type_mapping = {}
    for name, description in asset_types:
        asset_type, created = AssetType.objects.get_or_create(
            name=name,
            defaults={'description': description}
        )
        # Map old choice values to new objects
        asset_type_mapping[name.lower()] = asset_type

    # Create default locations for existing assets
    existing_locations = Asset.objects.values_list('location', flat=True).distinct()
    location_mapping = {}

    for loc_name in existing_locations:
        if loc_name and loc_name.strip():
            location, created = Location.objects.get_or_create(
                name=loc_name.strip(),
                defaults={'address': f'Location: {loc_name.strip()}'}
            )
            location_mapping[loc_name] = location

    # Update existing assets with new ForeignKey relationships
    for asset in Asset.objects.all():
        # Map asset_type choice to AssetType object
        if asset.asset_type in asset_type_mapping:
            asset.asset_type_fk = asset_type_mapping[asset.asset_type]

        # Map location string to Location object
        if asset.location and asset.location.strip() in location_mapping:
            asset.location_fk = location_mapping[asset.location.strip()]

        asset.save()


def reverse_populate_asset_types_and_locations(apps, schema_editor):
    # This is a data migration, so we don't need to reverse it
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('asset_management', '0003_assettype_location_asset_asset_type_fk_and_more'),
    ]

    operations = [
        migrations.RunPython(
            populate_asset_types_and_locations,
            reverse_populate_asset_types_and_locations
        ),
    ]
