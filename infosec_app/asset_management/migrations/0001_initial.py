# Generated by Django 4.2.7 on 2025-07-19 18:21

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Asset',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON><PERSON>(help_text='Asset name or identifier', max_length=200)),
                ('description', models.TextField(blank=True, help_text='Detailed description of the asset')),
                ('asset_type', models.CharField(choices=[('hardware', 'Hardware'), ('software', 'Software'), ('data', 'Data'), ('network', 'Network'), ('facility', 'Facility'), ('personnel', 'Personnel')], help_text='Type of asset', max_length=20)),
                ('model_number', models.CharField(blank=True, help_text='Model or version number', max_length=100)),
                ('serial_number', models.Char<PERSON><PERSON>(blank=True, help_text='Serial number or unique identifier', max_length=100)),
                ('location', models.CharField(blank=True, help_text='Physical or logical location', max_length=200)),
                ('owner', models.CharField(blank=True, help_text='Asset owner or responsible person', max_length=100)),
                ('criticality', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('critical', 'Critical')], default='medium', help_text='Business criticality level', max_length=20)),
                ('status', models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('maintenance', 'Under Maintenance'), ('decommissioned', 'Decommissioned')], default='active', help_text='Current operational status', max_length=20)),
                ('acquisition_date', models.DateField(blank=True, help_text='Date when asset was acquired', null=True)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('created_date', models.DateTimeField(auto_now_add=True)),
                ('notes', models.TextField(blank=True, help_text='Additional notes or comments')),
            ],
            options={
                'verbose_name': 'Asset',
                'verbose_name_plural': 'Assets',
                'ordering': ['name'],
            },
        ),
    ]
