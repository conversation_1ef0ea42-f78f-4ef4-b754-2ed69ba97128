# Generated by Django 4.2.7 on 2025-07-19 19:38

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('asset_management', '0002_remove_asset_model_number'),
    ]

    operations = [
        migrations.CreateModel(
            name='AssetType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Asset type name (e.g., Hardware, Software)', max_length=100, unique=True)),
                ('description', models.TextField(blank=True, help_text='Description of this asset type')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this asset type is currently in use')),
                ('created_date', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Asset Type',
                'verbose_name_plural': 'Asset Types',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Location',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Location name (e.g., Data Center A, Office Floor 2)', max_length=200, unique=True)),
                ('address', models.TextField(blank=True, help_text='Physical address or detailed location description')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this location is currently in use')),
                ('created_date', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Location',
                'verbose_name_plural': 'Locations',
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='asset',
            name='asset_type_fk',
            field=models.ForeignKey(blank=True, help_text='Type of asset (new)', null=True, on_delete=django.db.models.deletion.PROTECT, to='asset_management.assettype'),
        ),
        migrations.AddField(
            model_name='asset',
            name='location_fk',
            field=models.ForeignKey(blank=True, help_text='Physical or logical location (new)', null=True, on_delete=django.db.models.deletion.SET_NULL, to='asset_management.location'),
        ),
    ]
