from django.urls import path
from . import views

app_name = 'risk_management'

urlpatterns = [
    # Dashboard
    path('risks/', views.risk_dashboard_view, name='dashboard'),
    
    # Risk CRUD operations
    path('risks/list/', views.RiskListView.as_view(), name='risk_list'),
    path('risks/add/', views.RiskCreateView.as_view(), name='risk_create'),
    path('risks/<int:pk>/', views.RiskDetailView.as_view(), name='risk_detail'),
    path('risks/<int:pk>/edit/', views.RiskUpdateView.as_view(), name='risk_update'),
    path('risks/<int:pk>/delete/', views.RiskDeleteView.as_view(), name='risk_delete'),
]
