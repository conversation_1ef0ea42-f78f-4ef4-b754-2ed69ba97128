# Generated by Django 4.2.7 on 2025-07-19 18:34

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('asset_management', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Risk',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(help_text='Risk title or name', max_length=200)),
                ('description', models.TextField(help_text='Detailed description of the risk')),
                ('category', models.CharField(choices=[('confidentiality', 'Confidentiality'), ('integrity', 'Integrity'), ('availability', 'Availability'), ('compliance', 'Compliance'), ('operational', 'Operational'), ('financial', 'Financial'), ('reputational', 'Reputational')], help_text='Risk category', max_length=20)),
                ('probability', models.IntegerField(choices=[(1, 'Very Low (1)'), (2, 'Low (2)'), (3, 'Medium (3)'), (4, 'High (4)'), (5, 'Very High (5)')], default=3, help_text='Likelihood of the risk occurring (1-5)')),
                ('impact', models.IntegerField(choices=[(1, 'Very Low (1)'), (2, 'Low (2)'), (3, 'Medium (3)'), (4, 'High (4)'), (5, 'Very High (5)')], default=3, help_text='Impact if the risk occurs (1-5)')),
                ('status', models.CharField(choices=[('identified', 'Identified'), ('assessed', 'Assessed'), ('mitigated', 'Mitigated'), ('accepted', 'Accepted'), ('transferred', 'Transferred'), ('avoided', 'Avoided')], default='identified', help_text='Current status of risk management', max_length=20)),
                ('owner', models.CharField(blank=True, help_text='Person responsible for managing this risk', max_length=100)),
                ('mitigation_strategy', models.TextField(blank=True, help_text='Strategy to mitigate or manage the risk')),
                ('mitigation_cost', models.DecimalField(blank=True, decimal_places=2, help_text='Estimated cost of mitigation', max_digits=10, null=True)),
                ('target_date', models.DateField(blank=True, help_text='Target date for risk mitigation', null=True)),
                ('identified_date', models.DateField(default=django.utils.timezone.now, help_text='Date when risk was identified')),
                ('last_reviewed', models.DateField(blank=True, help_text='Date of last risk review', null=True)),
                ('created_date', models.DateTimeField(auto_now_add=True)),
                ('updated_date', models.DateTimeField(auto_now=True)),
                ('notes', models.TextField(blank=True, help_text='Additional notes or comments')),
                ('asset', models.ForeignKey(help_text='Asset associated with this risk', on_delete=django.db.models.deletion.CASCADE, related_name='risks', to='asset_management.asset')),
            ],
            options={
                'verbose_name': 'Risk',
                'verbose_name_plural': 'Risks',
                'ordering': ['-probability', '-impact', 'title'],
            },
        ),
    ]
