from django.db import models
from django.urls import reverse
from django.utils import timezone
from asset_management.models import Asset


class Risk(models.Model):
    """
    Model representing a security risk associated with an asset
    """
    RISK_CATEGORIES = [
        ('confidentiality', 'Confidentiality'),
        ('integrity', 'Integrity'),
        ('availability', 'Availability'),
        ('compliance', 'Compliance'),
        ('operational', 'Operational'),
        ('financial', 'Financial'),
        ('reputational', 'Reputational'),
    ]

    PROBABILITY_LEVELS = [
        (1, 'Very Low (1)'),
        (2, 'Low (2)'),
        (3, 'Medium (3)'),
        (4, 'High (4)'),
        (5, 'Very High (5)'),
    ]

    IMPACT_LEVELS = [
        (1, 'Very Low (1)'),
        (2, 'Low (2)'),
        (3, 'Medium (3)'),
        (4, 'High (4)'),
        (5, 'Very High (5)'),
    ]

    STATUS_CHOICES = [
        ('identified', 'Identified'),
        ('assessed', 'Assessed'),
        ('mitigated', 'Mitigated'),
        ('accepted', 'Accepted'),
        ('transferred', 'Transferred'),
        ('avoided', 'Avoided'),
    ]

    # Basic Information
    title = models.CharField(max_length=200, help_text="Risk title or name")
    description = models.TextField(help_text="Detailed description of the risk")
    category = models.CharField(max_length=20, choices=RISK_CATEGORIES, help_text="Risk category")

    # Asset Relationship
    asset = models.ForeignKey(Asset, on_delete=models.CASCADE, related_name='risks',
                             help_text="Asset associated with this risk")

    # Risk Assessment
    probability = models.IntegerField(choices=PROBABILITY_LEVELS, default=3,
                                    help_text="Likelihood of the risk occurring (1-5)")
    impact = models.IntegerField(choices=IMPACT_LEVELS, default=3,
                               help_text="Impact if the risk occurs (1-5)")

    # Risk Management
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='identified',
                            help_text="Current status of risk management")
    owner = models.CharField(max_length=100, blank=True,
                           help_text="Person responsible for managing this risk")

    # Mitigation
    mitigation_strategy = models.TextField(blank=True,
                                         help_text="Strategy to mitigate or manage the risk")
    mitigation_cost = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True,
                                        help_text="Estimated cost of mitigation")
    target_date = models.DateField(null=True, blank=True,
                                 help_text="Target date for risk mitigation")

    # Dates
    identified_date = models.DateField(default=timezone.now,
                                     help_text="Date when risk was identified")
    last_reviewed = models.DateField(null=True, blank=True,
                                   help_text="Date of last risk review")
    created_date = models.DateTimeField(auto_now_add=True)
    updated_date = models.DateTimeField(auto_now=True)

    # Additional Information
    notes = models.TextField(blank=True, help_text="Additional notes or comments")

    class Meta:
        ordering = ['-probability', '-impact', 'title']
        verbose_name = 'Risk'
        verbose_name_plural = 'Risks'

    def __str__(self):
        return f"{self.title} - {self.asset.name}"

    @property
    def risk_score(self):
        """Calculate risk score (probability × impact)"""
        return self.probability * self.impact

    @property
    def risk_level(self):
        """Determine risk level based on score"""
        score = self.risk_score
        if score >= 20:
            return 'Critical'
        elif score >= 15:
            return 'High'
        elif score >= 10:
            return 'Medium'
        elif score >= 5:
            return 'Low'
        else:
            return 'Very Low'

    @property
    def risk_level_class(self):
        """Return CSS class for risk level"""
        level = self.risk_level
        if level == 'Critical':
            return 'danger'
        elif level == 'High':
            return 'warning'
        elif level == 'Medium':
            return 'info'
        else:
            return 'success'

    def get_absolute_url(self):
        return reverse('risk_management:risk_detail', kwargs={'pk': self.pk})
