from django.shortcuts import render, get_object_or_404, redirect
from django.views.generic import List<PERSON>iew, DetailView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.db.models import Q, Avg, Count
from django.http import JsonResponse
from .models import Risk
from asset_management.models import Asset


class RiskListView(ListView):
    """
    View to display list of all risks with filtering and search
    """
    model = Risk
    template_name = 'risk_management/risk_list.html'
    context_object_name = 'risks'
    paginate_by = 20

    def get_queryset(self):
        queryset = Risk.objects.select_related('asset').all()

        # Search functionality
        search_query = self.request.GET.get('search')
        if search_query:
            queryset = queryset.filter(
                Q(title__icontains=search_query) |
                Q(description__icontains=search_query) |
                Q(asset__name__icontains=search_query) |
                Q(category__icontains=search_query) |
                Q(owner__icontains=search_query)
            )

        # Filter by risk level
        risk_level = self.request.GET.get('level')
        if risk_level:
            if risk_level == 'critical':
                queryset = [r for r in queryset if r.risk_score >= 20]
            elif risk_level == 'high':
                queryset = [r for r in queryset if 15 <= r.risk_score < 20]
            elif risk_level == 'medium':
                queryset = [r for r in queryset if 10 <= r.risk_score < 15]
            elif risk_level == 'low':
                queryset = [r for r in queryset if r.risk_score < 10]

        # Filter by status
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)

        # Filter by asset
        asset_id = self.request.GET.get('asset')
        if asset_id:
            queryset = queryset.filter(asset_id=asset_id)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_query'] = self.request.GET.get('search', '')
        context['selected_level'] = self.request.GET.get('level', '')
        context['selected_status'] = self.request.GET.get('status', '')
        context['selected_asset'] = self.request.GET.get('asset', '')
        context['assets'] = Asset.objects.all().order_by('name')
        context['status_choices'] = Risk.STATUS_CHOICES
        return context


class RiskDetailView(DetailView):
    """
    View to display detailed information about a specific risk
    """
    model = Risk
    template_name = 'risk_management/risk_detail.html'
    context_object_name = 'risk'


class RiskCreateView(CreateView):
    """
    View to create a new risk
    """
    model = Risk
    template_name = 'risk_management/risk_form.html'
    fields = [
        'title', 'description', 'category', 'asset', 'probability', 'impact',
        'status', 'owner', 'mitigation_strategy', 'mitigation_cost', 'target_date',
        'identified_date', 'last_reviewed', 'notes'
    ]
    success_url = reverse_lazy('risk_management:risk_list')

    def get_initial(self):
        initial = super().get_initial()
        # Pre-select asset if provided in URL
        asset_id = self.request.GET.get('asset')
        if asset_id:
            initial['asset'] = asset_id
        return initial

    def form_valid(self, form):
        messages.success(self.request, f'Risk "{form.instance.title}" has been created successfully.')
        return super().form_valid(form)


class RiskUpdateView(UpdateView):
    """
    View to update an existing risk
    """
    model = Risk
    template_name = 'risk_management/risk_form.html'
    fields = [
        'title', 'description', 'category', 'asset', 'probability', 'impact',
        'status', 'owner', 'mitigation_strategy', 'mitigation_cost', 'target_date',
        'identified_date', 'last_reviewed', 'notes'
    ]
    success_url = reverse_lazy('risk_management:risk_list')

    def form_valid(self, form):
        messages.success(self.request, f'Risk "{form.instance.title}" has been updated successfully.')
        return super().form_valid(form)


class RiskDeleteView(DeleteView):
    """
    View to delete a risk
    """
    model = Risk
    template_name = 'risk_management/risk_confirm_delete.html'
    success_url = reverse_lazy('risk_management:risk_list')

    def delete(self, request, *args, **kwargs):
        risk = self.get_object()
        messages.success(request, f'Risk "{risk.title}" has been deleted successfully.')
        return super().delete(request, *args, **kwargs)


def risk_dashboard_view(request):
    """
    Dashboard view showing risk statistics and analytics
    """
    total_risks = Risk.objects.count()

    # Risk level distribution
    risks = Risk.objects.all()
    risk_levels = {'Critical': 0, 'High': 0, 'Medium': 0, 'Low': 0, 'Very Low': 0}

    for risk in risks:
        risk_levels[risk.risk_level] += 1

    # Status distribution
    status_counts = {}
    for status_code, status_name in Risk.STATUS_CHOICES:
        count = Risk.objects.filter(status=status_code).count()
        status_counts[status_name] = count

    # Category distribution
    category_counts = {}
    for category_code, category_name in Risk.RISK_CATEGORIES:
        count = Risk.objects.filter(category=category_code).count()
        category_counts[category_name] = count

    # Recent risks
    recent_risks = Risk.objects.select_related('asset').order_by('-created_date')[:5]

    # High priority risks (score >= 15)
    high_priority_risks = [r for r in risks if r.risk_score >= 15]

    context = {
        'total_risks': total_risks,
        'risk_levels': risk_levels,
        'status_counts': status_counts,
        'category_counts': category_counts,
        'recent_risks': recent_risks,
        'high_priority_risks': high_priority_risks,
        'high_priority_count': len(high_priority_risks),
    }

    return render(request, 'risk_management/dashboard.html', context)
