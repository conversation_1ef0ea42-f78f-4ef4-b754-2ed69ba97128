from django.contrib import admin
from .models import Risk


@admin.register(Risk)
class RiskAdmin(admin.ModelAdmin):
    list_display = ['title', 'asset', 'category', 'risk_level', 'risk_score', 'status', 'owner', 'created_date']
    list_filter = ['category', 'status', 'probability', 'impact', 'created_date']
    search_fields = ['title', 'description', 'asset__name', 'owner']
    list_editable = ['status']
    date_hierarchy = 'created_date'
    ordering = ['-probability', '-impact', 'title']

    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'description', 'category', 'asset')
        }),
        ('Risk Assessment', {
            'fields': ('probability', 'impact'),
            'description': 'Rate probability and impact on a scale of 1-5'
        }),
        ('Risk Management', {
            'fields': ('status', 'owner', 'mitigation_strategy', 'mitigation_cost', 'target_date')
        }),
        ('Important Dates', {
            'fields': ('identified_date', 'last_reviewed')
        }),
        ('Additional Information', {
            'fields': ('notes',),
            'classes': ('collapse',)
        }),
    )

    def risk_level(self, obj):
        return obj.risk_level
    risk_level.short_description = 'Risk Level'

    def risk_score(self, obj):
        return obj.risk_score
    risk_score.short_description = 'Risk Score'
